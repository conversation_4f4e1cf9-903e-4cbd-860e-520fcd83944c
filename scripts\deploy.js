#!/usr/bin/env node

/**
 * Script de despliegue para La Legendaria Germayori
 * Configura automáticamente el dominio y las variables de entorno
 */

import fs from 'fs';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 Configurador de Dominio - La Legendaria Germayori\n');

function pregunta(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

async function configurarDominio() {
  try {
    // Preguntar por el dominio
    const dominio = await pregunta('📝 Ingresa tu dominio (ej: https://mi-dominio.com): ');
    
    if (!dominio || !dominio.startsWith('http')) {
      console.log('❌ Error: Debes ingresar un dominio válido que comience con http:// o https://');
      process.exit(1);
    }

    // Limpiar el dominio (quitar barra final si existe)
    const dominioLimpio = dominio.replace(/\/$/, '');

    console.log(`\n🔧 Configurando dominio: ${dominioLimpio}`);

    // Actualizar archivo de configuración
    const configPath = path.join(__dirname, '../src/config/config.js');
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    configContent = configContent.replace(
      /baseURL: 'https:\/\/tu-dominio\.com'/g,
      `baseURL: '${dominioLimpio}'`
    );
    configContent = configContent.replace(
      /apiURL: 'https:\/\/tu-dominio\.com\/api'/g,
      `apiURL: '${dominioLimpio}/api'`
    );
    configContent = configContent.replace(
      /dashboardURL: 'https:\/\/tu-dominio\.com\/dashboard'/g,
      `dashboardURL: '${dominioLimpio}/dashboard'`
    );
    configContent = configContent.replace(
      /loginURL: 'https:\/\/tu-dominio\.com\/login'/g,
      `loginURL: '${dominioLimpio}/login'`
    );

    fs.writeFileSync(configPath, configContent);

    // Actualizar .env.production
    const envProdPath = path.join(__dirname, '../.env.production');
    let envContent = fs.readFileSync(envProdPath, 'utf8');
    
    envContent = envContent.replace(
      /NEXT_PUBLIC_BASE_URL=https:\/\/tu-dominio\.com/g,
      `NEXT_PUBLIC_BASE_URL=${dominioLimpio}`
    );
    envContent = envContent.replace(
      /NEXT_PUBLIC_API_URL=https:\/\/tu-dominio\.com\/api/g,
      `NEXT_PUBLIC_API_URL=${dominioLimpio}/api`
    );

    fs.writeFileSync(envProdPath, envContent);

    // Actualizar next.config.js
    const nextConfigPath = path.join(__dirname, '../next.config.js');
    let nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');
    
    const domainOnly = dominioLimpio.replace(/https?:\/\//, '');
    nextConfigContent = nextConfigContent.replace(
      /domains: \['localhost', 'tu-dominio\.com'\]/g,
      `domains: ['localhost', '${domainOnly}']`
    );

    fs.writeFileSync(nextConfigPath, nextConfigContent);

    console.log('✅ Configuración completada exitosamente!');
    console.log('\n📋 Archivos actualizados:');
    console.log('   - src/config/config.js');
    console.log('   - .env.production');
    console.log('   - next.config.js');
    
    console.log('\n🚀 Próximos pasos:');
    console.log('   1. Ejecuta: npm run build');
    console.log('   2. Ejecuta: npm start');
    console.log('   3. Sube los archivos a tu servidor');
    console.log(`   4. Configura tu servidor para apuntar a ${dominioLimpio}`);
    
    console.log('\n💡 Nota: Asegúrate de que tu servidor esté configurado para servir archivos estáticos y manejar rutas de Next.js');

  } catch (error) {
    console.error('❌ Error durante la configuración:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Ejecutar configuración
configurarDominio();
