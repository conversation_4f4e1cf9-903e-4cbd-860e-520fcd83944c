import { connectToDatabase } from '../../lib/mongodb';

export default async function handler(req, res) {
  if (req.method === 'POST') {
    return await crearInversion(req, res);
  } else if (req.method === 'GET') {
    return await obtenerInversiones(req, res);
  } else if (req.method === 'PUT') {
    return await actualizarInversion(req, res);
  } else {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }
}

// Crear nueva inversión
async function crearInversion(req, res) {
  try {
    const { 
      usuarioId, 
      montoInicial, 
      rendimientoMensual, 
      rendimientoTrimestral,
      rendimientoAnual,
      rendimientoTotal2Anos,
      montoFinal,
      comprobantePago 
    } = req.body;

    // Validar datos requeridos
    if (!usuarioId || !montoInicial || montoInicial < 100) {
      return res.status(400).json({
        success: false,
        message: 'Datos de inversión inválidos. Monto mínimo: $100'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('inversiones');

    // Crear fechas
    const fechaActual = new Date();
    const fechaVencimiento = new Date();
    fechaVencimiento.setFullYear(fechaActual.getFullYear() + 2); // 2 años

    // Próximo retiro trimestral (3 meses)
    const proximoRetiro = new Date();
    proximoRetiro.setMonth(fechaActual.getMonth() + 3);

    // Crear nueva inversión
    const nuevaInversion = {
      usuarioId: usuarioId,
      montoInicial: parseFloat(montoInicial),
      rendimientoMensual: parseFloat(rendimientoMensual),
      rendimientoTrimestral: parseFloat(rendimientoTrimestral),
      rendimientoAnual: parseFloat(rendimientoAnual),
      rendimientoTotal2Anos: parseFloat(rendimientoTotal2Anos),
      montoFinal: parseFloat(montoFinal),
      fechaInicio: fechaActual.toISOString(),
      fechaVencimiento: fechaVencimiento.toISOString(),
      proximoRetiro: proximoRetiro.toISOString(),
      estado: 'pendiente_verificacion', // pendiente_verificacion, activa, completada, cancelada
      comprobantePago: comprobantePago || null,
      retirosRealizados: [],
      gananciaAcumulada: 0,
      fechaCreacion: fechaActual.toISOString()
    };

    // Insertar en MongoDB
    const resultado = await collection.insertOne(nuevaInversion);

    if (resultado.insertedId) {
      return res.status(201).json({
        success: true,
        message: 'Inversión registrada exitosamente',
        inversionId: resultado.insertedId,
        inversion: nuevaInversion
      });
    } else {
      throw new Error('No se pudo registrar la inversión');
    }

  } catch (error) {
    console.error('❌ Error creando inversión:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

// Obtener inversiones de un usuario
async function obtenerInversiones(req, res) {
  try {
    const { usuarioId } = req.query;

    if (!usuarioId) {
      return res.status(400).json({
        success: false,
        message: 'ID de usuario requerido'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('inversiones');

    // Buscar inversiones del usuario
    const inversiones = await collection.find({ usuarioId: usuarioId }).toArray();

    return res.status(200).json({
      success: true,
      inversiones: inversiones
    });

  } catch (error) {
    console.error('❌ Error obteniendo inversiones:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

// Actualizar inversión (para administradores)
async function actualizarInversion(req, res) {
  try {
    const { inversionId, estado, notas } = req.body;

    if (!inversionId || !estado) {
      return res.status(400).json({
        success: false,
        message: 'ID de inversión y estado son requeridos'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('inversiones');

    // Actualizar inversión
    const resultado = await collection.updateOne(
      { _id: inversionId },
      { 
        $set: { 
          estado: estado,
          notas: notas || '',
          fechaActualizacion: new Date().toISOString()
        } 
      }
    );

    if (resultado.modifiedCount > 0) {
      return res.status(200).json({
        success: true,
        message: 'Inversión actualizada exitosamente'
      });
    } else {
      return res.status(404).json({
        success: false,
        message: 'Inversión no encontrada'
      });
    }

  } catch (error) {
    console.error('❌ Error actualizando inversión:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}
