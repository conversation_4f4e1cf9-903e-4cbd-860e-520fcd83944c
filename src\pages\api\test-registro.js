// API de prueba para verificar el registro
export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    return res.status(200).json({
      success: true,
      message: 'API de registro funcionando',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Método no permitido' 
    });
  }

  try {
    console.log('🔄 TEST REGISTRO - Iniciando...');
    
    // Verificar datos recibidos
    const body = req.body;
    console.log('📝 Datos recibidos:', {
      hasNombre: !!body?.nombre,
      hasEmail: !!body?.email,
      hasTelefono: !!body?.telefono,
      hasEdad: !!body?.edad,
      hasPais: !!body?.pais,
      hasPassword: !!body?.password
    });

    // Validar campos requeridos
    const { nombre, email, telefono, edad, pais, password } = body;
    
    if (!nombre || !email || !telefono || !edad || !pais || !password) {
      console.log('❌ Faltan campos requeridos');
      return res.status(400).json({
        success: false,
        message: 'Todos los campos son requeridos',
        received: { nombre: !!nombre, email: !!email, telefono: !!telefono, edad: !!edad, pais: !!pais, password: !!password }
      });
    }

    console.log('✅ Todos los campos presentes');

    // Probar conexión a MongoDB
    console.log('🔄 Probando conexión a MongoDB...');
    
    const { MongoClient } = await import('mongodb');
    const uri = "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";
    
    const client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
    });
    
    await client.connect();
    console.log('✅ MongoDB conectado exitosamente');
    
    const db = client.db('legendaria-germayori');
    const collection = db.collection('usuarios');
    
    // Verificar si el email ya existe
    const usuarioExistente = await collection.findOne({ correo: email });
    if (usuarioExistente) {
      await client.close();
      return res.status(400).json({ 
        success: false, 
        message: 'Este email ya está registrado' 
      });
    }

    console.log('✅ Email disponible');

    // Encriptar contraseña
    const bcrypt = await import('bcryptjs');
    const hashedPassword = await bcrypt.default.hash(password, 10);
    console.log('✅ Contraseña encriptada');

    // Crear usuario de prueba
    const fechaActual = new Date();
    const nuevoUsuario = {
      nombre: nombre.trim(),
      correo: email.toLowerCase().trim(),
      password: hashedPassword,
      celular: telefono.trim(),
      edad: parseInt(edad),
      pais: pais,
      fechaRegistro: fechaActual.toISOString(),
      plan: "PREMIUM",
      estado: "inactivo",
      fechaExpiracion: null,
      rol: "usuario",
      pagado: false,
      fechaPago: null,
      activadoPor: null,
      fechaActivacion: null,
      testUser: true // Marcar como usuario de prueba
    };

    // Insertar en MongoDB
    const resultado = await collection.insertOne(nuevoUsuario);
    console.log('✅ Usuario insertado:', resultado.insertedId);

    await client.close();
    console.log('✅ Conexión cerrada');

    return res.status(201).json({
      success: true,
      message: 'Usuario de prueba registrado exitosamente',
      userId: resultado.insertedId,
      nombre: nuevoUsuario.nombre,
      email: nuevoUsuario.correo,
      test: true
    });

  } catch (error) {
    console.error('❌ Error en test-registro:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
