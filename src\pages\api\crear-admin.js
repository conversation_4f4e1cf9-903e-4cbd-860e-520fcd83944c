import { connectToDatabase } from '../../lib/mongodb';
import bcrypt from 'bcryptjs';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { nombre, email, password, claveSecreta } = req.body;

    // Clave secreta para crear administradores (puedes cambiarla)
    const CLAVE_ADMIN = 'GERMAYORI_ADMIN_2024';
    
    if (claveSecreta !== CLAVE_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Clave secreta incorrecta. Solo el propietario puede crear administradores.'
      });
    }

    // Validar datos requeridos
    if (!nombre || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nombre, email y contraseña son requeridos'
      });
    }

    // Validar contraseña
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'La contraseña debe tener al menos 6 caracteres'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Verificar si el email ya existe
    const usuarioExistente = await collection.findOne({ correo: email.toLowerCase().trim() });
    if (usuarioExistente) {
      return res.status(400).json({ 
        success: false, 
        message: 'Este email ya está registrado' 
      });
    }

    // Encriptar contraseña
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Crear fechas (administradores no expiran)
    const fechaActual = new Date();
    const fechaExpiracion = new Date();
    fechaExpiracion.setFullYear(fechaActual.getFullYear() + 10); // 10 años

    // Crear nuevo administrador
    const nuevoAdmin = {
      nombre: nombre.trim(),
      correo: email.toLowerCase().trim(),
      password: hashedPassword,
      celular: "N/A",
      edad: 0,
      pais: "Administrador",
      fechaRegistro: fechaActual.toISOString(),
      plan: "ADMIN",
      estado: "activo",
      fechaExpiracion: fechaExpiracion.toISOString(),
      rol: "administrador"
    };

    // Insertar en MongoDB
    const resultado = await collection.insertOne(nuevoAdmin);

    if (resultado.insertedId) {
      return res.status(201).json({
        success: true,
        message: 'Administrador creado exitosamente',
        adminId: resultado.insertedId,
        nombre: nuevoAdmin.nombre,
        email: nuevoAdmin.correo,
        rol: nuevoAdmin.rol
      });
    } else {
      throw new Error('No se pudo crear el administrador');
    }

  } catch (error) {
    console.error('❌ Error creando administrador:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}
