/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  
  // Configuración de variables de entorno públicas
  env: {
    CUSTOM_KEY: 'legendaria-germayori',
  },

  // Configuración de imágenes
  images: {
    domains: ['localhost', 'germayori.com', 'www.germayori.com'], // Dominio configurado
    unoptimized: true
  },

  // Configuración de headers de seguridad
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },

  // Configuración de redirects
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
    ]
  },

  // Configuración de rewrites para API
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ]
  },

  // Configuración de exportación estática (opcional)
  trailingSlash: false,
  
  // Configuración de webpack personalizada
  webpack: (config) => {
    // Configuraciones personalizadas de webpack si es necesario
    return config
  },
}

export default nextConfig
