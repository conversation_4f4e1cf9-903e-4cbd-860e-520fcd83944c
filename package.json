{"name": "legendaria", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "deploy:config": "node scripts/deploy.js", "build:prod": "NODE_ENV=production next build", "start:prod": "NODE_ENV=production next start -p 3002"}, "dependencies": {"bcryptjs": "^3.0.2", "formidable": "^3.5.4", "mongodb": "^6.17.0", "multer": "^2.0.1", "next": "15.3.3", "openai": "^4.104.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.5", "tailwindcss": "^3.4.0"}}