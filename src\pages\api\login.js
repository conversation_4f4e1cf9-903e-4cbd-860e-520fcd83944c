import { connectToDatabase } from '../../lib/mongodb';
import bcrypt from 'bcryptjs';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { email, password } = req.body;

    // Validar datos requeridos
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email y contraseña son requeridos'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Buscar usuario por email
    const usuario = await collection.findOne({ 
      correo: email.toLowerCase().trim() 
    });

    if (!usuario) {
      return res.status(401).json({
        success: false,
        message: 'Usuario no encontrado. ¿Ya te registraste y pagaste?'
      });
    }

    // Verificar contraseña
    const passwordValida = await bcrypt.compare(password, usuario.password);
    if (!passwordValida) {
      return res.status(401).json({
        success: false,
        message: 'Contraseña incorrecta'
      });
    }

    // Verificar si el usuario está activo
    if (usuario.estado !== 'activo') {
      return res.status(401).json({ 
        success: false, 
        message: 'Tu cuenta no está activa. Verifica tu pago con los administradores.' 
      });
    }

    // Verificar si la suscripción no ha expirado
    const fechaActual = new Date();
    const fechaExpiracion = new Date(usuario.fechaExpiracion);
    
    if (fechaActual > fechaExpiracion) {
      // Actualizar estado a expirado
      await collection.updateOne(
        { _id: usuario._id },
        { $set: { estado: 'expirado' } }
      );
      
      return res.status(401).json({ 
        success: false, 
        message: 'Tu suscripción ha expirado. Renueva tu acceso.' 
      });
    }

    // Login exitoso - devolver datos del usuario
    return res.status(200).json({
      success: true,
      message: 'Login exitoso',
      usuario: {
        id: usuario._id,
        nombre: usuario.nombre,
        correo: usuario.correo,
        plan: usuario.plan,
        fechaExpiracion: usuario.fechaExpiracion,
        estado: usuario.estado,
        rol: usuario.rol || 'usuario', // Incluir rol
        fotoPerfil: usuario.fotoPerfil || null, // Incluir foto de perfil
        celular: usuario.celular || null,
        edad: usuario.edad || null,
        pais: usuario.pais || null,
        fechaRegistro: usuario.fechaRegistro
      }
    });

  } catch (error) {
    console.error('❌ Error en login:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}
