import { connectToDatabase } from '../../lib/mongodb';
import bcrypt from 'bcryptjs';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { nombre, email, telefono, edad, pais, password } = req.body;

    // Validar datos requeridos
    if (!nombre || !email || !telefono || !edad || !pais || !password) {
      return res.status(400).json({
        success: false,
        message: 'Todos los campos son requeridos, incluyendo la contraseña'
      });
    }

    // Validar contraseña
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'La contraseña debe tener al menos 6 caracteres'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Verificar si el email ya existe
    const usuarioExistente = await collection.findOne({ correo: email });
    if (usuarioExistente) {
      return res.status(400).json({ 
        success: false, 
        message: 'Este email ya está registrado' 
      });
    }

    // Encriptar contraseña
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Crear fechas
    const fechaActual = new Date();
    const fechaExpiracion = new Date();
    fechaExpiracion.setDate(fechaActual.getDate() + 30); // 30 días SOLO después del pago

    // Crear nuevo usuario - DESACTIVADO hasta activación manual
    const nuevoUsuario = {
      nombre: nombre.trim(),
      correo: email.toLowerCase().trim(),
      password: hashedPassword, // Contraseña encriptada
      celular: telefono.trim(),
      edad: parseInt(edad),
      pais: pais,
      fechaRegistro: fechaActual.toISOString(),
      plan: "PREMIUM", // Plan que tendrá cuando se active
      estado: "inactivo", // DESACTIVADO - Tú lo activarás manualmente
      fechaExpiracion: null, // Sin fecha hasta que lo actives
      fechaPagoEsperada: fechaExpiracion.toISOString(), // Cuando expirará si lo activas por 30 días
      rol: "usuario", // Rol por defecto
      pagado: false, // Indicador de pago
      fechaPago: null, // Fecha cuando se verifique el pago
      activadoPor: null, // Quién lo activó
      fechaActivacion: null // Cuándo fue activado
    };

    // Insertar en MongoDB
    const resultado = await collection.insertOne(nuevoUsuario);

    if (resultado.insertedId) {
      return res.status(201).json({
        success: true,
        message: 'Usuario registrado exitosamente',
        userId: resultado.insertedId,
        nombre: nuevoUsuario.nombre,
        email: nuevoUsuario.correo,
        fechaExpiracion: fechaExpiracion.toISOString()
      });
    } else {
      throw new Error('No se pudo insertar el usuario');
    }

  } catch (error) {
    console.error('❌ Error en registro:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}
