import { connectToDatabase } from '../../lib/mongodb';

export default async function handler(req, res) {
  try {
    console.log('🔄 Intentando conectar a MongoDB...');
    console.log('🌍 Environment:', process.env.NODE_ENV);
    console.log('🔗 MongoDB URI exists:', !!process.env.MONGODB_URI);
    console.log('🗄️ MongoDB DB:', process.env.MONGODB_DB || 'legendaria-germayori');

    const { db } = await connectToDatabase();
    console.log('✅ Conexión exitosa a MongoDB');

    const collection = db.collection('usuarios');
    const count = await collection.countDocuments();

    console.log(`📊 Usuarios en la base de datos: ${count}`);

    // Obtener algunos usuarios de ejemplo (sin contraseñas)
    const usuarios = await collection.find({}, {
      projection: {
        nombre: 1,
        correo: 1,
        estado: 1,
        fechaRegistro: 1,
        plan: 1,
        pais: 1
      }
    }).limit(5).toArray();

    return res.status(200).json({
      success: true,
      message: 'Conexión exitosa a MongoDB',
      usuariosCount: count,
      database: 'legendaria-germayori',
      collection: 'usuarios',
      environment: process.env.NODE_ENV,
      hasMongoUri: !!process.env.MONGODB_URI,
      mongoDb: process.env.MONGODB_DB || 'legendaria-germayori',
      usuariosEjemplo: usuarios
    });

  } catch (error) {
    console.error('❌ Error conectando a MongoDB:', error);
    return res.status(500).json({
      success: false,
      message: 'Error de conexión a MongoDB',
      error: error.message,
      stack: error.stack,
      environment: process.env.NODE_ENV,
      hasMongoUri: !!process.env.MONGODB_URI,
      mongoDb: process.env.MONGODB_DB || 'legendaria-germayori'
    });
  }
}
