import { connectToDatabase } from '../../../lib/mongodb';

export default async function handler(req, res) {
  if (req.method === 'GET') {
    return await obtenerTodasInversiones(req, res);
  } else {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }
}

// Obtener todas las inversiones (solo para administradores)
async function obtenerTodasInversiones(req, res) {
  try {
    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const inversionesCollection = db.collection('inversiones');
    const usuariosCollection = db.collection('usuarios');

    // Obtener todas las inversiones
    const inversiones = await inversionesCollection.find({}).toArray();

    // Enriquecer con datos del usuario
    const inversionesConUsuario = await Promise.all(
      inversiones.map(async (inversion) => {
        const usuario = await usuariosCollection.findOne(
          { _id: inversion.usuarioId },
          { projection: { nombre: 1, correo: 1 } }
        );
        
        return {
          ...inversion,
          usuarioNombre: usuario?.nombre || 'Usuario no encontrado',
          usuarioEmail: usuario?.correo || 'Email no encontrado'
        };
      })
    );

    return res.status(200).json({
      success: true,
      inversiones: inversionesConUsuario
    });

  } catch (error) {
    console.error('❌ Error obteniendo inversiones:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}
