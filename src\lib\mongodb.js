import { MongoClient } from 'mongodb';

// URI de MongoDB - SIEMPRE usar la variable de entorno en producción
const uri = process.env.MONGODB_URI || "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";

const options = {
  serverSelectionTimeoutMS: 10000, // 10 segundos para selección de servidor
  socketTimeoutMS: 45000, // 45 segundos para timeout de socket
  maxPoolSize: 10, // Máximo 10 conexiones en el pool
  minPoolSize: 5, // Mínimo 5 conexiones en el pool
  maxIdleTimeMS: 30000, // Cerrar conexiones después de 30s de inactividad
  bufferMaxEntries: 0, // Deshabilitar mongoose buffering
  useNewUrlParser: true,
  useUnifiedTopology: true,
};

let client;
let clientPromise;

// Configuración diferente para desarrollo vs producción
if (process.env.NODE_ENV === 'development') {
  // En desarrollo, usar variable global para evitar múltiples conexiones
  if (!global._mongoClientPromise) {
    console.log('🔄 Creando nueva conexión MongoDB (desarrollo)');
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  // En producción, crear nueva conexión cada vez
  console.log('🔄 Creando nueva conexión MongoDB (producción)');
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

// Función para conectar a la base de datos
export async function connectToDatabase() {
  try {
    console.log('🔄 Intentando conectar a MongoDB...');
    console.log('🌍 Entorno:', process.env.NODE_ENV);
    console.log('🔗 URI existe:', !!process.env.MONGODB_URI);

    const client = await clientPromise;
    console.log('✅ Cliente MongoDB conectado');

    const dbName = process.env.MONGODB_DB || 'legendaria-germayori';
    const db = client.db(dbName);
    console.log('✅ Base de datos seleccionada:', dbName);

    // Probar la conexión con un ping
    await db.admin().ping();
    console.log('✅ Ping exitoso a MongoDB');

    return { client, db };
  } catch (error) {
    console.error('❌ Error conectando a MongoDB:', error);
    console.error('🔗 URI utilizada:', uri.replace(/\/\/.*:.*@/, '//***:***@'));
    console.error('🌍 NODE_ENV:', process.env.NODE_ENV);
    console.error('🗄️ MONGODB_DB:', process.env.MONGODB_DB);
    throw new Error(`Error de conexión MongoDB: ${error.message}`);
  }
}

// Exportar el clientPromise por defecto
export default clientPromise;
