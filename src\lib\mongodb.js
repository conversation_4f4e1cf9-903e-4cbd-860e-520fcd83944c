import { MongoClient } from 'mongodb';

// URI de MongoDB - FUNCIONAL Y SIMPLE
const uri = "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";

const options = {
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
};

let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export async function connectToDatabase() {
  try {
    const client = await clientPromise;
    const db = client.db('legendaria-germayori');
    return { client, db };
  } catch (error) {
    console.error('Error conectando a MongoDB:', error);
    throw error;
  }
}

// Exportar el clientPromise por defecto
export default clientPromise;
