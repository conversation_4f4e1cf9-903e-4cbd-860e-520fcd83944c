import { MongoClient } from 'mongodb';

// URI de MongoDB - CON CONTRASEÑA CORRECTA
const uri = process.env.MONGODB_URI || "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";

const options = {
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 45000,
  maxPoolSize: 10,
  minPoolSize: 5,
};

let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export async function connectToDatabase() {
  try {
    console.log('🔄 Conectando a MongoDB...');
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'legendaria-germayori');
    console.log('✅ Conectado a MongoDB exitosamente');
    return { client, db };
  } catch (error) {
    console.error('❌ Error conectando a MongoDB:', error);
    console.error('🔗 URI:', uri.replace(/\/\/.*:.*@/, '//***:***@'));
    throw new Error(`MongoDB Error: ${error.message}`);
  }
}

// Exportar el clientPromise por defecto
export default clientPromise;
