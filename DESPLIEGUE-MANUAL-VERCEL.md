# 🚀 Despliegue Manual en Vercel - germayori.com

## ✅ Tu aplicación está 100% lista para desplegar

### **OPCIÓN A: Despliegue desde GitHub (RECOMENDADO)**

#### Paso 1: Subir a GitHub
1. Ve a [github.com](https://github.com) y crea un nuevo repositorio llamado `germayori`
2. En tu terminal, ejecuta:
```bash
git init
git add .
git commit -m "Aplicación Germayori lista para producción"
git branch -M main
git remote add origin https://github.com/TU_USUARIO/germayori.git
git push -u origin main
```

#### Paso 2: Conectar con Vercel
1. Ve a [vercel.com](https://vercel.com)
2. Haz clic en "New Project"
3. Conecta tu cuenta de GitHub
4. Selecciona el repositorio `germayori`
5. Haz clic en "Deploy"

#### Paso 3: Configurar Variables de Entorno
En Vercel → Settings → Environment Variables, agrega:

```
MONGODB_URI
mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI

OPENAI_API_KEY
********************************************************************************************************************************************************************

NEXT_PUBLIC_BASE_URL
https://germayori.com

NEXT_PUBLIC_API_URL
https://germayori.com/api

JWT_SECRET
clave_super_secreta_para_produccion_2024_muy_larga_y_segura

SESSION_SECRET
otra_clave_super_secreta_para_sesiones_produccion_2024

NODE_ENV
production
```

#### Paso 4: Configurar Dominio Personalizado
1. En Vercel → Settings → Domains
2. Agregar: `germayori.com`
3. Agregar: `www.germayori.com`
4. Vercel te dará instrucciones de DNS

---

### **OPCIÓN B: Subida Directa de Archivos**

#### Paso 1: Comprimir proyecto
```bash
# Crear archivo ZIP excluyendo node_modules
tar -czf germayori.tar.gz --exclude=node_modules --exclude=.git .
```

#### Paso 2: Subir a Vercel
1. Ve a [vercel.com](https://vercel.com)
2. Arrastra el archivo `germayori.tar.gz` a la página
3. Vercel detectará automáticamente que es un proyecto Next.js

#### Paso 3: Configurar variables (igual que Opción A)

---

## 🔧 Configuración DNS para germayori.com

Una vez desplegado, configura estos registros DNS en tu proveedor de dominio:

### Registros DNS:
```
Tipo: A
Nombre: @
Valor: ***********

Tipo: CNAME  
Nombre: www
Valor: cname.vercel-dns.com
```

---

## 🧪 Verificar Funcionamiento

Una vez desplegado, verifica que funcione:

1. **Página principal**: https://germayori.com ✅
2. **Registro**: Llenar formulario en la página principal ✅
3. **Login**: https://germayori.com/login ✅
4. **Dashboard**: https://germayori.com/dashboard ✅
5. **Canal de señales**: Subir imagen en el dashboard ✅
6. **Base de datos**: Los usuarios se guardan en MongoDB ✅

---

## 🎯 URLs Importantes

Después del despliegue tendrás:
- **Sitio principal**: https://germayori.com
- **Panel de administración**: https://germayori.com/crear-admin
- **Login**: https://germayori.com/login
- **Dashboard**: https://germayori.com/dashboard

---

## 🆘 Solución de Problemas

### Error: "Environment variables not found"
- Verifica que todas las variables estén configuradas en Vercel
- Redespliega el proyecto después de agregar variables

### Error: "Cannot connect to MongoDB"
- Verifica que la IP de Vercel esté en la whitelist de MongoDB Atlas
- MongoDB Atlas → Network Access → Add IP Address → 0.0.0.0/0 (Allow all)

### Error: "OpenAI API not working"
- Verifica que la clave de OpenAI sea válida
- Verifica que tengas créditos en tu cuenta de OpenAI

---

## 🎉 ¡Listo!

Tu aplicación Germayori estará funcionando completamente en https://germayori.com con:
- ✅ Registro y login de usuarios
- ✅ Base de datos MongoDB
- ✅ Canal de señales con OpenAI
- ✅ Sistema de pagos
- ✅ Dashboard completo
- ✅ Panel administrativo

**¡Tu negocio de trading estará online y funcionando!** 🚀
