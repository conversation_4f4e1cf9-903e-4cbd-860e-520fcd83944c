@echo off
echo 🚀 Subiendo Germayori a GitHub...
echo ================================

echo 📦 Inicializando repositorio Git...
git init

echo 📝 Agregando archivos...
git add .

echo 💾 Creando commit...
git commit -m "Aplicacion Germayori lista para produccion - Canal de senales funcionando"

echo 🌿 Configurando rama principal...
git branch -M main

echo 📡 Conectando con GitHub...
echo.
echo ⚠️  IMPORTANTE: Crea un repositorio en GitHub llamado 'germayori' primero
echo    Ve a: https://github.com/new
echo    Nombre: germayori
echo    Descripcion: Plataforma de Trading Germayori
echo    Publico o Privado: Tu eleccion
echo.
pause

echo 🔗 Agrega la URL de tu repositorio:
set /p REPO_URL="Pega aqui la URL de tu repositorio GitHub: "

git remote add origin %REPO_URL%

echo 🚀 Subiendo a GitHub...
git push -u origin main

echo.
echo ✅ ¡Proyecto subido a GitHub exitosamente!
echo.
echo 📋 Proximos pasos:
echo 1. Ve a vercel.com
echo 2. Haz clic en "New Project"
echo 3. Conecta tu repositorio de GitHub
echo 4. Configura las variables de entorno
echo 5. Agrega el dominio germayori.com
echo.
echo 🎉 ¡Tu aplicacion estara en https://germayori.com!
pause
